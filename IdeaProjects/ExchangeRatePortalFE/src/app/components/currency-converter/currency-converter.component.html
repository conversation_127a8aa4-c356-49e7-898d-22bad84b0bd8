<div class="row">
  <div class="col-md-8 mx-auto">
    <div class="card">
      <div class="card-header">
        <h2>Currency Converter</h2>
      </div>
      <div class="card-body">
        <div *ngIf="error" class="alert alert-danger">{{ error }}</div>

        <div *ngIf="loadingCurrencies" class="d-flex justify-content-center my-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading currencies...</span>
          </div>
        </div>

        <form [formGroup]="converterForm" (ngSubmit)="onSubmit()" *ngIf="!loadingCurrencies">
          <div class="row mb-3">
            <div class="col-md-5">
              <label for="amount" class="form-label">Amount</label>
              <input
                type="number"
                class="form-control"
                id="amount"
                formControlName="amount"
                min="0.01"
                step="0.01"
              >
              <div *ngIf="converterForm.get('amount')?.invalid && converterForm.get('amount')?.touched" class="text-danger mt-1">
                Amount is required and must be greater than 0.
              </div>
            </div>

            <div class="col-md-5">
              <label for="fromCurrency" class="form-label">From Currency</label>
              <select class="form-select" id="fromCurrency" formControlName="fromCurrency">
                <option *ngFor="let currency of currencies" [value]="currency.code">
                  {{ currency.code }}
                </option>
              </select>
            </div>

            <div class="col-md-2 d-flex align-items-end justify-content-center">
              <button type="button" class="btn btn-outline-secondary mb-3" (click)="swapCurrencies()">
                <i class="bi bi-arrow-left-right"></i> Swap
              </button>
            </div>
          </div>

          <div class="row mb-4">
            <div class="col-md-5 offset-md-5">
              <label for="toCurrency" class="form-label">To Currency</label>
              <select class="form-select" id="toCurrency" formControlName="toCurrency">
                <option *ngFor="let currency of currencies" [value]="currency.code">
                  {{ currency.code }}
                </option>
              </select>
            </div>
          </div>

          <div class="d-grid">
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="converterForm.invalid || loading"
            >
              <span *ngIf="loading" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
              Convert
            </button>
          </div>
        </form>

        <div *ngIf="conversionResult" class="mt-4 p-3 bg-light rounded">
          <h3 class="mb-3">Conversion Result</h3>
          <div class="row">
            <div class="col-md-6">
              <p class="mb-1">From:</p>
              <h4>{{ conversionResult.amount | number:'1.2-2' }} {{ conversionResult.fromCurrency }}</h4>
            </div>
            <div class="col-md-6">
              <p class="mb-1">To:</p>
              <h4>{{ conversionResult.convertedAmount | number:'1.2-2' }} {{ conversionResult.toCurrency }}</h4>
            </div>
          </div>
          <div class="mt-3">
            <p class="mb-1">Exchange Rate:</p>
            <p>1 {{ conversionResult.fromCurrency }} = {{ conversionResult.rate | number:'1.0-6' }} {{ conversionResult.toCurrency }}</p>
            <p class="text-muted small">Conversion date: {{ conversionResult.conversionDate | date:'medium' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
