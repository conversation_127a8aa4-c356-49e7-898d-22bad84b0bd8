<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h2>{{ currencyCode }} - {{ currencyName }} Exchange Rate History</h2>
        <div>
          <a routerLink="/exchange-rates" class="btn btn-outline-primary">Back to Rates</a>
        </div>
      </div>
      <div class="card-body">
        <div *ngIf="error" class="alert alert-danger">{{ error }}</div>

        <div *ngIf="loading" class="d-flex justify-content-center my-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>

        <div *ngIf="!loading && exchangeRates.length === 0" class="alert alert-info">
          No historical data available for {{ currencyCode }}.
        </div>

        <div *ngIf="!loading && exchangeRates.length > 0">
          <div class="chart-container">
            <canvas baseChart
              [data]="lineChartData"
              [options]="lineChartOptions"
              [type]="'line'">
            </canvas>
          </div>

          <h3 class="mt-4">Historical Data</h3>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Rate (1 EUR =)</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let rate of exchangeRates">
                  <td>{{ rate.date | date:'mediumDate' }}</td>
                  <td>{{ rate.rate | number:'1.0-6' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
